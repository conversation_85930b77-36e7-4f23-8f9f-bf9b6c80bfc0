#!/usr/bin/env python3
"""
Test version of the agent with a simpler query to avoid rate limits
and better LangSmith tracing visibility.
"""

import os

from dotenv import load_dotenv

load_dotenv()

from langchain import hub
from langchain.agents import AgentExecutor
from langchain.agents.react.agent import create_react_agent
from langchain_openai import ChatOpenAI
from langchain_tavily import TavilySearch
# Import LangSmith for explicit tracing configuration
from langsmith import Client

# Configure <PERSON>Smith tracing explicitly
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_API_KEY"] = os.environ.get("LANGSMITH_API_KEY", "")
os.environ["LANGCHAIN_PROJECT"] = os.environ.get("LANGSMITH_PROJECT_NAME", "default")

# Initialize LangSmith client to verify connection
try:
    client = Client(
        api_key=os.environ.get("LANGSMITH_API_KEY"),
        api_url=os.environ.get("LANGSMITH_ENDPOINT", "https://api.smith.langchain.com"),
    )
    print(f"✅ LangSmith client initialized successfully")
    print(f"📊 Project: {os.environ.get('LANGSMITH_PROJECT_NAME', 'default')}")
except Exception as e:
    print(f"❌ LangSmith initialization failed: {e}")

# Initialize tools and LLM
tools = [TavilySearch()]
llm = ChatOpenAI(
    model="gpt-4",
    api_key=os.environ.get("OPENAI_API_KEY"),
    base_url=os.environ.get("OPENAI_BASE_URL"),
    temperature=0,  # Make it more deterministic
)

# Get the ReAct prompt
react_prompt = hub.pull("hwchase17/react")

# Create agent and executor
agent = create_react_agent(llm=llm, tools=tools, prompt=react_prompt)
agent_executor = AgentExecutor(
    agent=agent,
    tools=tools,
    verbose=True,
    handle_parsing_errors=True,
    max_iterations=3,  # Limit iterations to reduce API calls
    early_stopping_method="generate",  # Stop early if possible
)


def test_simple_query():
    """Test with a simple query that requires fewer API calls."""
    print("🚀 Starting simple agent test...")
    print(f"🔍 Query: What is LangChain?")

    try:
        result = agent_executor.invoke(
            input={"input": "What is LangChain? Give me a brief explanation."}
        )
        print("✅ Agent execution completed successfully!")
        print("📋 Result:")
        print(result)
        print(
            f"\n📊 Check your LangSmith project '{os.environ.get('LANGSMITH_PROJECT_NAME', 'default')}' for detailed traces!"
        )
        return result
    except Exception as e:
        print(f"❌ Agent execution failed: {e}")
        print(f"🔧 Error type: {type(e).__name__}")

        # Check if it's a rate limit error
        if "429" in str(e) or "rate limit" in str(e).lower():
            print("\n💡 Rate Limit Detected!")
            print("   - Your API provider has daily limits")
            print("   - Wait until tomorrow or upgrade your API plan")
            print("   - Even failed attempts should show up in LangSmith traces")

        return None


def test_without_tools():
    """Test a simple LLM call without tools to minimize API usage."""
    print("\n🧪 Testing simple LLM call without tools...")

    try:
        # Create a simple chain without tools
        from langchain_core.output_parsers import StrOutputParser
        from langchain_core.prompts import ChatPromptTemplate

        prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    "You are a helpful assistant. Answer briefly and concisely.",
                ),
                ("human", "{input}"),
            ]
        )

        chain = prompt | llm | StrOutputParser()

        result = chain.invoke({"input": "What is 2+2?"})
        print(f"✅ Simple LLM call completed: {result}")
        print(f"📊 This should also appear in LangSmith traces!")
        return result

    except Exception as e:
        print(f"❌ Simple LLM call failed: {e}")
        return None


if __name__ == "__main__":
    print("🔧 Testing Agent with LangSmith Tracing")
    print("=" * 50)

    # First try the simple LLM call
    test_without_tools()

    # Then try the agent (might hit rate limits)
    test_simple_query()

    print("\n" + "=" * 50)
    print("🎯 Key Points for LangSmith Tracing:")
    print("1. ✅ LangSmith is properly configured")
    print("2. 📊 Traces should appear in your 'search-agent' project")
    print("3. 🔍 Even failed runs due to rate limits create traces")
    print("4. 🌐 Visit https://smith.langchain.com to view traces")
    print("5. 🔑 Make sure you're logged into the correct LangSmith account")
