# Lang<PERSON>mith Tracing Troubleshooting Guide

## Why You Might Not See Agent Outputs in LangSmith Traces

### 1. **Configuration Issues** ✅ FIXED
- ❌ Missing `LANGCHAIN_TRACING_V2=true`
- ❌ Incorrect project name with spaces
- ❌ Missing explicit LangSmith setup
- ✅ **Solution**: Updated your code with proper configuration

### 2. **API Failures Preventing Completion**
- ❌ Rate limits (5 requests/day on your current plan)
- ❌ Authentication errors
- ⚠️ **Impact**: Even failed runs create traces, but they're incomplete

### 3. **Common LangSmith Visibility Issues**

#### A. Wrong Project or Account
- Check you're viewing the correct project: `search-agent`
- Verify you're logged into the right LangSmith account
- URL: https://smith.langchain.com/

#### B. Trace Filtering
- Lang<PERSON>mith might be filtering out failed traces
- Look for traces with error status
- Check the time range filter

#### C. Delayed Trace Appearance
- Traces can take 30-60 seconds to appear
- Refresh the page after running your agent

### 4. **What Should Appear in Traces**

Even with API failures, you should see:
- ✅ Agent initialization
- ✅ Prompt construction
- ✅ Tool selection attempts
- ❌ Incomplete tool execution (due to API limits)
- ❌ Partial reasoning chains

### 5. **Immediate Action Items**

1. **Check LangSmith Dashboard**
   - Go to: https://smith.langchain.com/
   - Navigate to project: `search-agent`
   - Look for recent traces (even failed ones)

2. **Verify Environment**
   ```bash
   python test_langsmith.py
   ```

3. **Test with Working API**
   - Get a working OpenAI API key, or
   - Use a different model provider, or
   - Test with mock responses

### 6. **Alternative Testing Approach**

If you want to see complete traces immediately:

```python
# Use a mock tool instead of TavilySearch
from langchain.tools import tool

@tool
def mock_search(query: str) -> str:
    """Mock search tool that doesn't require external APIs."""
    return f"Mock search results for: {query}"

# Replace TavilySearch with mock_search
tools = [mock_search]
```

### 7. **Expected Trace Structure**

In a successful agent run, you should see:
```
📊 Trace Hierarchy:
├── AgentExecutor
│   ├── Agent Planning
│   │   ├── Prompt Template
│   │   ├── LLM Call (GPT-4)
│   │   └── Output Parsing
│   ├── Tool Execution
│   │   ├── Tool Selection
│   │   ├── Tool Input Preparation
│   │   ├── Tool Call (TavilySearch)
│   │   └── Tool Output
│   └── Final Answer Generation
│       ├── LLM Call (GPT-4)
│       └── Final Response
```

### 8. **Debug Commands**

Run these to verify your setup:

```bash
# Test LangSmith connectivity
python test_langsmith.py

# Test agent with better error handling
python test_agent.py

# Check environment variables
python -c "import os; from dotenv import load_dotenv; load_dotenv(); print('LangSmith Project:', os.environ.get('LANGSMITH_PROJECT_NAME'))"
```

### 9. **Next Steps**

1. **First**: Check your LangSmith dashboard for any traces
2. **If no traces**: Run `test_langsmith.py` and verify the simple trace appears
3. **If traces appear but incomplete**: The issue is API limits, not LangSmith
4. **If still no traces**: Check your LangSmith account and project settings

### 10. **Common Gotchas**

- **Project names**: Avoid spaces and special characters
- **API keys**: Make sure LANGSMITH_API_KEY is correct
- **Network**: Corporate firewalls might block LangSmith
- **Timing**: Traces appear with a delay, be patient
- **Filtering**: LangSmith UI might hide failed traces by default

---

## Quick Verification Checklist

- [ ] LangSmith API key is valid
- [ ] Project name is `search-agent` (no spaces)
- [ ] `LANGCHAIN_TRACING_V2=true` is set
- [ ] Simple trace test passes (`test_langsmith.py`)
- [ ] Checked LangSmith dashboard within 2 minutes of running agent
- [ ] Looked for traces with error status, not just successful ones
- [ ] Verified correct LangSmith account/organization
