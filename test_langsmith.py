#!/usr/bin/env python3
"""
Test script to verify Lang<PERSON>mith tracing configuration and connectivity.
This script tests Lang<PERSON>mith without making expensive API calls.
"""

import os

from dotenv import load_dotenv

# Load environment variables
load_dotenv()


def test_langsmith_config():
    """Test LangSmith configuration and connectivity."""
    print("🔍 Testing LangSmith Configuration...")
    print("=" * 50)

    # Check environment variables
    required_vars = [
        "LANGSMITH_API_KEY",
        "LANGSMITH_TRACING",
        "LANGSMITH_ENDPOINT",
        "LANGSMITH_PROJECT_NAME",
    ]

    print("📋 Environment Variables:")
    for var in required_vars:
        value = os.environ.get(var)
        if value:
            # Mask API key for security
            if "API_KEY" in var:
                masked_value = (
                    f"{value[:8]}...{value[-4:]}" if len(value) > 12 else "***"
                )
                print(f"  ✅ {var}: {masked_value}")
            else:
                print(f"  ✅ {var}: {value}")
        else:
            print(f"  ❌ {var}: Not set")

    print("\n🔗 Testing LangSmith Client Connection...")

    try:
        from langsmith import Client

        # Initialize client
        client = Client(
            api_key=os.environ.get("LANGSMITH_API_KEY"),
            api_url=os.environ.get(
                "LANGSMITH_ENDPOINT", "https://api.smith.langchain.com"
            ),
        )

        # Test basic connectivity by trying to get info about the current user/org
        try:
            # This is a lightweight call that doesn't consume quota
            info = client.info()
            print(f"  ✅ Successfully connected to LangSmith!")
            print(f"  📊 Organization: {info.get('organization_id', 'N/A')}")
            print(f"  👤 User: {info.get('user_id', 'N/A')}")
        except Exception as e:
            print(f"  ⚠️  Connected to LangSmith but couldn't get info: {e}")

    except ImportError:
        print("  ❌ LangSmith package not installed")
        return False
    except Exception as e:
        print(f"  ❌ Failed to connect to LangSmith: {e}")
        return False

    print("\n🧪 Testing LangChain Tracing Setup...")

    # Set up LangChain tracing environment variables
    os.environ["LANGCHAIN_TRACING_V2"] = "true"
    os.environ["LANGCHAIN_API_KEY"] = os.environ.get("LANGSMITH_API_KEY", "")
    os.environ["LANGCHAIN_PROJECT"] = os.environ.get(
        "LANGSMITH_PROJECT_NAME", "default"
    )

    try:
        from langchain_core.tracers.langchain import LangChainTracer

        tracer = LangChainTracer(
            project_name=os.environ.get("LANGSMITH_PROJECT_NAME", "default")
        )
        print(f"  ✅ LangChain tracer initialized successfully")
        print(f"  📁 Project: {os.environ.get('LANGSMITH_PROJECT_NAME', 'default')}")

    except Exception as e:
        print(f"  ❌ Failed to initialize LangChain tracer: {e}")
        return False

    print("\n✅ LangSmith configuration test completed successfully!")
    print("\n💡 Tips for troubleshooting:")
    print("  1. Make sure your LANGSMITH_API_KEY is valid")
    print("  2. Check that your project name doesn't contain special characters")
    print("  3. Verify your network can reach https://api.smith.langchain.com")
    print("  4. Ensure LANGCHAIN_TRACING_V2=true is set before running your agent")

    return True


def test_simple_trace():
    """Test a simple trace to LangSmith without expensive API calls."""
    print("\n🧪 Testing Simple Trace...")
    print("=" * 30)

    try:
        from langchain_core.output_parsers import StrOutputParser
        from langchain_core.runnables import RunnableLambda

        # Create a simple runnable that doesn't require external APIs
        def simple_function(input_text):
            return f"Processed: {input_text}"

        # Create a simple chain
        chain = RunnableLambda(simple_function) | StrOutputParser()

        # Run the chain - this should create a trace in LangSmith
        result = chain.invoke("Hello LangSmith!")

        print(f"  ✅ Simple trace completed: {result}")
        print(
            f"  📊 Check your LangSmith project '{os.environ.get('LANGSMITH_PROJECT_NAME', 'default')}' for the trace"
        )

        return True

    except Exception as e:
        print(f"  ❌ Simple trace failed: {e}")
        return False


if __name__ == "__main__":
    success = test_langsmith_config()
    if success:
        test_simple_trace()
    else:
        print(
            "\n❌ LangSmith configuration test failed. Please fix the issues above before running your agent."
        )
