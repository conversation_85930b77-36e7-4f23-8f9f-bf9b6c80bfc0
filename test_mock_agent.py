#!/usr/bin/env python3
"""
Test agent with mock tools to avoid API limits and ensure LangSmith traces appear.
This version should definitely create visible traces in LangSmith.
"""

import os

from dotenv import load_dotenv

load_dotenv()

# Configure Lang<PERSON>mith tracing FIRST
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_API_KEY"] = os.environ.get("LANGSMITH_API_KEY", "")
os.environ["LANGCHAIN_PROJECT"] = os.environ.get("LANGSMITH_PROJECT_NAME", "default")

from langchain import hub
from langchain.agents import AgentExecutor, create_react_agent
from langchain.tools import tool
from langchain_core.language_models.fake import FakeListLLM

print("🔧 Testing Mock Agent with LangSmith Tracing")
print("=" * 50)
print(f"📊 Project: {os.environ.get('LANGSMITH_PROJECT_NAME', 'default')}")
print(f"🔍 Tracing enabled: {os.environ.get('LANGCHAIN_TRACING_V2', 'false')}")


# Create a mock search tool that doesn't require external APIs
@tool
def mock_search(query: str) -> str:
    """Mock search tool that simulates job search results."""
    return f"""
    Found 3 AI Engineer positions in Bay Area:
    
    1. Senior AI Engineer at TechCorp
       - Location: San Francisco, CA
       - Requirements: Python, LangChain, ML experience
       - Salary: $150k-200k
    
    2. AI/ML Engineer at StartupXYZ  
       - Location: Palo Alto, CA
       - Requirements: PyTorch, LangChain, 3+ years exp
       - Salary: $140k-180k
       
    3. Principal AI Engineer at BigTech
       - Location: Mountain View, CA
       - Requirements: LangChain, distributed systems
       - Salary: $200k-250k
    
    Search completed for query: {query}
    """


# Create a fake LLM that gives predictable responses
fake_responses = [
    "I need to search for AI engineer jobs in the Bay Area using LangChain. Let me use the search tool.",
    "Action: mock_search\nAction Input: AI engineer jobs Bay Area LangChain",
    "Based on the search results, I found 3 relevant AI Engineer positions in the Bay Area that require LangChain experience:\n\n1. **Senior AI Engineer at TechCorp** (San Francisco)\n   - Salary: $150k-200k\n   - Requirements: Python, LangChain, ML experience\n\n2. **AI/ML Engineer at StartupXYZ** (Palo Alto)\n   - Salary: $140k-180k  \n   - Requirements: PyTorch, LangChain, 3+ years experience\n\n3. **Principal AI Engineer at BigTech** (Mountain View)\n   - Salary: $200k-250k\n   - Requirements: LangChain, distributed systems\n\nAll positions are in the Bay Area and specifically mention LangChain as a requirement.",
]

# Use FakeListLLM to avoid API calls entirely
llm = FakeListLLM(responses=fake_responses)

# Create tools list
tools = [mock_search]

# Get the ReAct prompt
try:
    react_prompt = hub.pull("hwchase17/react")
    print("✅ Successfully loaded ReAct prompt from hub")
except Exception as e:
    print(f"⚠️  Failed to load prompt from hub: {e}")
    # Fallback to a simple prompt
    from langchain_core.prompts import PromptTemplate

    react_prompt = PromptTemplate(
        input_variables=["tools", "tool_names", "input", "agent_scratchpad"],
        template="""Answer the following questions as best you can. You have access to the following tools:

{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Begin!

Question: {input}
Thought:{agent_scratchpad}""",
    )
    print("✅ Using fallback prompt")

# Create agent and executor
agent = create_react_agent(llm=llm, tools=tools, prompt=react_prompt)
agent_executor = AgentExecutor(
    agent=agent, tools=tools, verbose=True, handle_parsing_errors=True, max_iterations=3
)


def test_mock_agent():
    """Test the mock agent - this should definitely create traces."""
    print("\n🚀 Starting mock agent test...")
    print("🔍 Query: Find AI engineer jobs using LangChain in Bay Area")

    try:
        result = agent_executor.invoke(
            {
                "input": "Find 3 AI engineer job postings that require LangChain experience in the Bay Area and provide their details."
            }
        )

        print("\n✅ Mock agent execution completed successfully!")
        print("📋 Result:")
        print(result.get("output", "No output key found"))

        print(
            f"\n📊 Check your LangSmith project '{os.environ.get('LANGSMITH_PROJECT_NAME', 'default')}' for detailed traces!"
        )
        print("🌐 Visit: https://smith.langchain.com/")

        return result

    except Exception as e:
        print(f"❌ Mock agent execution failed: {e}")
        print(f"🔧 Error type: {type(e).__name__}")
        return None


if __name__ == "__main__":
    # Test the mock agent
    result = test_mock_agent()

    print("\n" + "=" * 60)
    print("🎯 LangSmith Trace Verification:")
    print("1. 📊 Go to https://smith.langchain.com/")
    print(
        f"2. 🔍 Navigate to project: '{os.environ.get('LANGSMITH_PROJECT_NAME', 'default')}'"
    )
    print("3. 🕐 Look for traces from the last few minutes")
    print("4. 📝 You should see:")
    print("   - AgentExecutor trace")
    print("   - Mock search tool calls")
    print("   - LLM interactions (fake responses)")
    print("   - Complete reasoning chain")
    print("\n💡 If you still don't see traces, there might be:")
    print("   - Network connectivity issues")
    print("   - LangSmith account/project access issues")
    print("   - Browser cache issues (try incognito mode)")

    if result:
        print("\n✅ Mock agent test completed successfully!")
    else:
        print("\n❌ Mock agent test failed - check error messages above")
